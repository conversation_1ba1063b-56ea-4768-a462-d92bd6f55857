using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using Npgsql;
using ProManage.Modules.Models;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Services;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Specialized database service for RBAC permission operations.
    /// Handles all database interactions for roles, permissions, and user access control.
    /// Follows ProManage's centralized database architecture using PostgreSQL.
    /// </summary>
    public class PermissionDatabaseService
    {
        /// <summary>
        /// Verify and create RBAC database schema if needed
        /// </summary>
        public static bool VerifyAndCreateSchema()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if tables exist and create them if needed
                    CreateRolesTableIfNotExists(connection);
                    CreateRolePermissionsTableIfNotExists(connection);
                    CreateUserPermissionsTableIfNotExists(connection);
                    CreateGlobalPermissionsTableIfNotExists(connection);
                    AddRoleIdToUsersIfNotExists(connection);

                    Debug.WriteLine("RBAC schema verification completed successfully");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying RBAC schema: {ex.Message}");
                return false;
            }
        }

        private static void CreateRolesTableIfNotExists(NpgsqlConnection connection)
        {
            const string createTableQuery = @"
                CREATE TABLE IF NOT EXISTS roles (
                    role_id SERIAL PRIMARY KEY,
                    role_name VARCHAR(50) NOT NULL UNIQUE,
                    description VARCHAR(255),
                    is_active BOOLEAN DEFAULT TRUE,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )";

            using (var command = new NpgsqlCommand(createTableQuery, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void CreateRolePermissionsTableIfNotExists(NpgsqlConnection connection)
        {
            const string createTableQuery = @"
                CREATE TABLE IF NOT EXISTS role_permissions (
                    permission_id SERIAL PRIMARY KEY,
                    role_id INTEGER NOT NULL,
                    form_name VARCHAR(100) NOT NULL,
                    read_permission BOOLEAN DEFAULT FALSE,
                    new_permission BOOLEAN DEFAULT FALSE,
                    edit_permission BOOLEAN DEFAULT FALSE,
                    delete_permission BOOLEAN DEFAULT FALSE,
                    print_permission BOOLEAN DEFAULT FALSE,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
                    UNIQUE(role_id, form_name)
                )";

            using (var command = new NpgsqlCommand(createTableQuery, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void CreateUserPermissionsTableIfNotExists(NpgsqlConnection connection)
        {
            const string createTableQuery = @"
                CREATE TABLE IF NOT EXISTS user_permissions (
                    perm_id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    form_name VARCHAR(100) NOT NULL,
                    read_permission BOOLEAN NULL,
                    new_permission BOOLEAN NULL,
                    edit_permission BOOLEAN NULL,
                    delete_permission BOOLEAN NULL,
                    print_permission BOOLEAN NULL,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    UNIQUE(user_id, form_name)
                )";

            using (var command = new NpgsqlCommand(createTableQuery, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void CreateGlobalPermissionsTableIfNotExists(NpgsqlConnection connection)
        {
            const string createTableQuery = @"
                CREATE TABLE IF NOT EXISTS global_permissions (
                    global_permission_id SERIAL PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    can_create_users BOOLEAN DEFAULT FALSE,
                    can_edit_users BOOLEAN DEFAULT FALSE,
                    can_delete_users BOOLEAN DEFAULT FALSE,
                    can_print_users BOOLEAN DEFAULT FALSE,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
                    UNIQUE(user_id)
                )";

            using (var command = new NpgsqlCommand(createTableQuery, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void AddRoleIdToUsersIfNotExists(NpgsqlConnection connection)
        {
            const string checkColumnQuery = @"
                SELECT COUNT(*) FROM information_schema.columns
                WHERE table_schema = 'public'
                AND table_name = 'users'
                AND column_name = 'role_id'";

            using (var command = new NpgsqlCommand(checkColumnQuery, connection))
            {
                var columnExists = Convert.ToInt32(command.ExecuteScalar()) > 0;

                if (!columnExists)
                {
                    const string addColumnQuery = @"
                        ALTER TABLE users ADD COLUMN role_id INTEGER;
                        ALTER TABLE users ADD CONSTRAINT FK_users_roles
                            FOREIGN KEY (role_id) REFERENCES roles(role_id)";

                    using (var addCommand = new NpgsqlCommand(addColumnQuery, connection))
                    {
                        addCommand.ExecuteNonQuery();
                    }
                }
            }
        }
        /// <summary>
        /// Get all active roles
        /// </summary>
        /// <returns>List of active roles</returns>
        public static List<Role> GetAllRoles()
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, updated_date
                FROM roles
                WHERE is_active = true
                ORDER BY role_name";

            var roles = new List<Role>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                roles.Add(new Role
                                {
                                    RoleId = reader.GetInt32(reader.GetOrdinal("role_id")),
                                    RoleName = reader.GetString(reader.GetOrdinal("role_name")),
                                    Description = reader.IsDBNull(reader.GetOrdinal("description")) ? null : reader.GetString(reader.GetOrdinal("description")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("is_active")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date")),
                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("updated_date"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all roles: {ex.Message}");
                throw;
            }

            return roles;
        }
        
        /// <summary>
        /// Get role by ID
        /// </summary>
        /// <param name="roleId">Role ID to find</param>
        /// <returns>Role object or null if not found</returns>
        public static Role GetRoleById(int roleId)
        {
            const string query = @"
                SELECT role_id, role_name, description, is_active, created_date, updated_date
                FROM roles
                WHERE role_id = @roleId";
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Role
                                {
                                    RoleId = reader.GetInt32(reader.GetOrdinal("role_id")),
                                    RoleName = reader.GetString(reader.GetOrdinal("role_name")),
                                    Description = reader.IsDBNull(reader.GetOrdinal("description")) ? null : reader.GetString(reader.GetOrdinal("description")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("is_active")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date")),
                                    ModifiedDate = reader.GetDateTime(reader.GetOrdinal("updated_date"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role by ID {roleId}: {ex.Message}");
                throw;
            }
            
            return null;
        }
        
        /// <summary>
        /// Get role permissions for specific role
        /// </summary>
        /// <param name="roleId">Role ID to get permissions for</param>
        /// <returns>List of role permissions</returns>
        public static List<RolePermission> GetRolePermissions(int roleId)
        {
            const string query = @"
                SELECT perm_id, role_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions
                WHERE role_id = @roleId
                ORDER BY form_name";
            
            var permissions = new List<RolePermission>();
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new RolePermission
                                {
                                    PermissionId = reader.GetInt32(reader.GetOrdinal("perm_id")),
                                    RoleId = reader.GetInt32(reader.GetOrdinal("role_id")),
                                    FormName = reader.GetString(reader.GetOrdinal("form_name")),
                                    ReadPermission = reader.GetBoolean(reader.GetOrdinal("read_permission")),
                                    NewPermission = reader.GetBoolean(reader.GetOrdinal("new_permission")),
                                    EditPermission = reader.GetBoolean(reader.GetOrdinal("edit_permission")),
                                    DeletePermission = reader.GetBoolean(reader.GetOrdinal("delete_permission")),
                                    PrintPermission = reader.GetBoolean(reader.GetOrdinal("print_permission")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permissions for role {roleId}: {ex.Message}");
                throw;
            }
            
            return permissions;
        }
        
        /// <summary>
        /// Get specific role permission
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>Role permission or null if not found</returns>
        public static RolePermission GetRolePermission(int roleId, string formName)
        {
            const string query = @"
                SELECT perm_id, role_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM role_permissions
                WHERE role_id = @roleId AND form_name = @formName";
            
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        command.Parameters.AddWithValue("@formName", formName);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new RolePermission
                                {
                                    PermissionId = reader.GetInt32(reader.GetOrdinal("perm_id")),
                                    RoleId = reader.GetInt32(reader.GetOrdinal("role_id")),
                                    FormName = reader.GetString(reader.GetOrdinal("form_name")),
                                    ReadPermission = reader.GetBoolean(reader.GetOrdinal("read_permission")),
                                    NewPermission = reader.GetBoolean(reader.GetOrdinal("new_permission")),
                                    EditPermission = reader.GetBoolean(reader.GetOrdinal("edit_permission")),
                                    DeletePermission = reader.GetBoolean(reader.GetOrdinal("delete_permission")),
                                    PrintPermission = reader.GetBoolean(reader.GetOrdinal("print_permission")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role permission for role {roleId}, form {formName}: {ex.Message}");
                throw;
            }
            
            return null;
        }
        
        /// <summary>
        /// Update role permissions in batch
        /// </summary>
        /// <param name="updates">List of permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateRolePermissions(List<RolePermissionUpdate> updates)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var update in updates)
                            {
                                const string query = @"
                                    INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                                    VALUES (@roleId, @formName, @readPermission, @newPermission, @editPermission, @deletePermission, @printPermission)
                                    ON CONFLICT (role_id, form_name)
                                    DO UPDATE SET
                                        read_permission = @readPermission,
                                        new_permission = @newPermission,
                                        edit_permission = @editPermission,
                                        delete_permission = @deletePermission,
                                        print_permission = @printPermission";

                                using (var command = new NpgsqlCommand(query, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@roleId", update.RoleId);
                                    command.Parameters.AddWithValue("@formName", update.FormName);
                                    command.Parameters.AddWithValue("@readPermission", update.ReadPermission);
                                    command.Parameters.AddWithValue("@newPermission", update.NewPermission);
                                    command.Parameters.AddWithValue("@editPermission", update.EditPermission);
                                    command.Parameters.AddWithValue("@deletePermission", update.DeletePermission);
                                    command.Parameters.AddWithValue("@printPermission", update.PrintPermission);

                                    command.ExecuteNonQuery();
                                }
                            }
                            
                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating role permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user permissions (overrides only)
        /// </summary>
        /// <param name="userId">User ID to get permissions for</param>
        /// <returns>List of user permission overrides</returns>
        public static List<UserPermission> GetUserPermissions(int userId)
        {
            const string query = @"
                SELECT perm_id, user_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions
                WHERE user_id = @userId
                ORDER BY form_name";

            var permissions = new List<UserPermission>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new UserPermission
                                {
                                    UserPermissionId = reader.GetInt32(reader.GetOrdinal("perm_id")),
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    FormName = reader.GetString(reader.GetOrdinal("form_name")),
                                    ReadPermission = reader.IsDBNull(reader.GetOrdinal("read_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("read_permission")),
                                    NewPermission = reader.IsDBNull(reader.GetOrdinal("new_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("new_permission")),
                                    EditPermission = reader.IsDBNull(reader.GetOrdinal("edit_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("edit_permission")),
                                    DeletePermission = reader.IsDBNull(reader.GetOrdinal("delete_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("delete_permission")),
                                    PrintPermission = reader.IsDBNull(reader.GetOrdinal("print_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("print_permission")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date"))
                                });
                            }
                        }
                    }
                }
            }
            catch (PostgresException ex) when (ex.SqlState == "42703") // Column does not exist
            {
                Debug.WriteLine($"Database schema issue detected: {ex.Message}");
                Debug.WriteLine("Attempting to create missing RBAC schema...");

                // Try to create the schema and retry
                if (VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Schema created successfully, retrying query...");
                    return GetUserPermissions(userId); // Recursive call after schema fix
                }
                else
                {
                    Debug.WriteLine("Failed to create schema");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permissions for user {userId}: {ex.Message}");
                throw;
            }

            return permissions;
        }

        /// <summary>
        /// Get specific user permission
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>User permission or null if not found</returns>
        public static UserPermission GetUserPermission(int userId, string formName)
        {
            const string query = @"
                SELECT perm_id, user_id, form_name, read_permission, new_permission,
                       edit_permission, delete_permission, print_permission, created_date
                FROM user_permissions
                WHERE user_id = @userId AND form_name = @formName";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.Parameters.AddWithValue("@formName", formName);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new UserPermission
                                {
                                    UserPermissionId = reader.GetInt32(reader.GetOrdinal("perm_id")),
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    FormName = reader.GetString(reader.GetOrdinal("form_name")),
                                    ReadPermission = reader.IsDBNull(reader.GetOrdinal("read_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("read_permission")),
                                    NewPermission = reader.IsDBNull(reader.GetOrdinal("new_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("new_permission")),
                                    EditPermission = reader.IsDBNull(reader.GetOrdinal("edit_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("edit_permission")),
                                    DeletePermission = reader.IsDBNull(reader.GetOrdinal("delete_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("delete_permission")),
                                    PrintPermission = reader.IsDBNull(reader.GetOrdinal("print_permission")) ? (bool?)null : reader.GetBoolean(reader.GetOrdinal("print_permission")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user permission for user {userId}, form {formName}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Get global permissions for user
        /// </summary>
        /// <param name="userId">User ID to get global permissions for</param>
        /// <returns>Global permission object or null if not found</returns>
        public static GlobalPermission GetGlobalPermissions(int userId)
        {
            const string query = @"
                SELECT perm_id, user_id, can_create_users, can_edit_users,
                       can_delete_users, can_print_users, created_date
                FROM global_permissions
                WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new GlobalPermission
                                {
                                    GlobalPermissionId = reader.GetInt32(reader.GetOrdinal("perm_id")),
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    CanCreateUsers = reader.GetBoolean(reader.GetOrdinal("can_create_users")),
                                    CanEditUsers = reader.GetBoolean(reader.GetOrdinal("can_edit_users")),
                                    CanDeleteUsers = reader.GetBoolean(reader.GetOrdinal("can_delete_users")),
                                    CanPrintUsers = reader.GetBoolean(reader.GetOrdinal("can_print_users")),
                                    CreatedDate = reader.GetDateTime(reader.GetOrdinal("created_date"))
                                };
                            }
                        }
                    }
                }
            }
            catch (PostgresException ex) when (ex.SqlState == "42703") // Column does not exist
            {
                Debug.WriteLine($"Database schema issue detected: {ex.Message}");
                Debug.WriteLine("Attempting to create missing RBAC schema...");

                // Try to create the schema and retry
                if (VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Schema created successfully, retrying query...");
                    return GetGlobalPermissions(userId); // Recursive call after schema fix
                }
                else
                {
                    Debug.WriteLine("Failed to create schema");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting global permissions for user {userId}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Add form permissions for all roles
        /// </summary>
        /// <param name="formName">Name of the form to add</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool AddFormToPermissionSystem(string formName)
        {
            const string query = @"
                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                SELECT role_id, @formName, false, false, false, false, false
                FROM roles
                WHERE NOT EXISTS (
                    SELECT 1 FROM role_permissions
                    WHERE role_id = roles.role_id AND form_name = @formName
                )";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@formName", formName);
                        return command.ExecuteNonQuery() > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error adding form {formName} to permission system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove form from permission system
        /// </summary>
        /// <param name="formName">Name of the form to remove</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveFormFromPermissionSystem(string formName)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Remove user permissions first
                            const string deleteUserPerms = "DELETE FROM user_permissions WHERE form_name = @formName";
                            using (var command = new NpgsqlCommand(deleteUserPerms, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@formName", formName);
                                command.ExecuteNonQuery();
                            }

                            // Remove role permissions
                            const string deleteRolePerms = "DELETE FROM role_permissions WHERE form_name = @formName";
                            using (var command = new NpgsqlCommand(deleteRolePerms, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@formName", formName);
                                command.ExecuteNonQuery();
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing form {formName} from permission system: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update user permission overrides
        /// </summary>
        /// <param name="updates">List of user permission updates</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateUserPermissions(List<UserPermissionUpdate> updates)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var update in updates)
                            {
                                const string query = @"
                                    INSERT INTO user_permissions (user_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                                    VALUES (@userId, @formName, @readPermission, @newPermission, @editPermission, @deletePermission, @printPermission)
                                    ON CONFLICT (user_id, form_name)
                                    DO UPDATE SET
                                        read_permission = @readPermission,
                                        new_permission = @newPermission,
                                        edit_permission = @editPermission,
                                        delete_permission = @deletePermission,
                                        print_permission = @printPermission";

                                using (var command = new NpgsqlCommand(query, connection, transaction))
                                {
                                    command.Parameters.AddWithValue("@userId", update.UserId);
                                    command.Parameters.AddWithValue("@formName", update.FormName);
                                    command.Parameters.AddWithValue("@readPermission", (object)update.ReadPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@newPermission", (object)update.NewPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@editPermission", (object)update.EditPermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@deletePermission", (object)update.DeletePermission ?? DBNull.Value);
                                    command.Parameters.AddWithValue("@printPermission", (object)update.PrintPermission ?? DBNull.Value);

                                    command.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                            Debug.WriteLine($"Updated {updates.Count} user permission records");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                            throw;
                        }
                    }
                }
            }
            catch (PostgresException ex) when (ex.SqlState == "23503") // Foreign key violation
            {
                Debug.WriteLine($"Foreign key constraint violation: {ex.Message}");
                Debug.WriteLine("This usually means the user_id doesn't exist in the users table");
                return false;
            }
            catch (PostgresException ex) when (ex.SqlState == "42703") // Column does not exist
            {
                Debug.WriteLine($"Database schema issue detected: {ex.Message}");
                Debug.WriteLine("Attempting to create missing RBAC schema...");

                // Try to create the schema and retry
                if (VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Schema created successfully, retrying update...");
                    return UpdateUserPermissions(updates); // Recursive call after schema fix
                }
                else
                {
                    Debug.WriteLine("Failed to create schema");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get user with role information
        /// </summary>
        /// <param name="userId">User ID to get role information for</param>
        /// <returns>User with role information or null if not found</returns>
        public static UserWithRole GetUserWithRole(int userId)
        {
            const string query = @"
                SELECT u.user_id, u.role_id, r.role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var roleId = reader.IsDBNull(reader.GetOrdinal("role_id")) ? (int?)null : reader.GetInt32(reader.GetOrdinal("role_id"));

                                return new UserWithRole
                                {
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    RoleId = roleId ?? 0, // Default to 0 if no role assigned
                                    RoleName = reader.IsDBNull(reader.GetOrdinal("role_name")) ? null : reader.GetString(reader.GetOrdinal("role_name"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting user with role for user {userId}: {ex.Message}");
                throw;
            }

            return null;
        }

        /// <summary>
        /// Update global permissions for a user
        /// </summary>
        /// <param name="update">Global permission update</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool UpdateGlobalPermissions(GlobalPermissionUpdate update)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    const string query = @"
                        INSERT INTO global_permissions (user_id, can_create_users, can_edit_users, can_delete_users, can_print_users)
                        VALUES (@userId, @canCreateUsers, @canEditUsers, @canDeleteUsers, @canPrintUsers)
                        ON CONFLICT (user_id)
                        DO UPDATE SET
                            can_create_users = @canCreateUsers,
                            can_edit_users = @canEditUsers,
                            can_delete_users = @canDeleteUsers,
                            can_print_users = @canPrintUsers";

                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", update.UserId);
                        command.Parameters.AddWithValue("@canCreateUsers", update.CanCreateUsers);
                        command.Parameters.AddWithValue("@canEditUsers", update.CanEditUsers);
                        command.Parameters.AddWithValue("@canDeleteUsers", update.CanDeleteUsers);
                        command.Parameters.AddWithValue("@canPrintUsers", update.CanPrintUsers);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Updated global permissions for user {update.UserId}, {rowsAffected} rows affected");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (PostgresException ex) when (ex.SqlState == "23503") // Foreign key violation
            {
                Debug.WriteLine($"Foreign key constraint violation: {ex.Message}");
                Debug.WriteLine("This usually means the user_id doesn't exist in the users table");
                return false;
            }
            catch (PostgresException ex) when (ex.SqlState == "42703") // Column does not exist
            {
                Debug.WriteLine($"Database schema issue detected: {ex.Message}");
                Debug.WriteLine("Attempting to create missing RBAC schema...");

                // Try to create the schema and retry
                if (VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Schema created successfully, retrying update...");
                    return UpdateGlobalPermissions(update); // Recursive call after schema fix
                }
                else
                {
                    Debug.WriteLine("Failed to create schema");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating global permissions for user {update.UserId}: {ex.Message}");
                return false;
            }
        }

        #region User Permission Override Operations

        /// <summary>
        /// Remove user permission override (revert to role permission)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveUserPermissionOverride(int userId, string formName)
        {
            const string query = "DELETE FROM user_permissions WHERE user_id = @userId AND form_name = @formName";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.Parameters.AddWithValue("@formName", formName);

                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed user permission override for user {userId}, form {formName}");
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing user permission override for user {userId}, form {formName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove all user permission overrides for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveAllUserPermissionOverrides(int userId)
        {
            const string query = "DELETE FROM user_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed {rowsAffected} user permission overrides for user {userId}");
                        return true; // Success even if 0 rows affected (no overrides to remove)
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing all user permission overrides for user {userId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Remove user permissions (alias for RemoveAllUserPermissionOverrides for test compatibility)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveUserPermissions(int userId)
        {
            return RemoveAllUserPermissionOverrides(userId);
        }

        #endregion

        #region Global Permission Operations

        /// <summary>
        /// Remove global permissions for user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool RemoveGlobalPermissions(int userId)
        {
            const string query = "DELETE FROM global_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        int rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Removed global permissions for user {userId}");
                        return rowsAffected >= 0; // Success even if 0 rows affected
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error removing global permissions for user {userId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset user permissions to role defaults (remove all user overrides)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool ResetUserPermissions(int userId)
        {
            const string query = @"DELETE FROM user_permissions WHERE user_id = @userId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        var rowsAffected = command.ExecuteNonQuery();
                        Debug.WriteLine($"Reset permissions for user {userId}, {rowsAffected} rows affected");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting user permissions: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Form Management

        /// <summary>
        /// Get all distinct form names from the permission system
        /// </summary>
        /// <returns>List of form names</returns>
        public static List<string> GetAllFormsFromDatabase()
        {
            const string query = @"
                SELECT DISTINCT form_name
                FROM role_permissions
                ORDER BY form_name";

            var formNames = new List<string>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                formNames.Add(reader.GetString(reader.GetOrdinal("form_name")));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all forms from database: {ex.Message}");
                throw;
            }

            return formNames;
        }

        #endregion

        #region Bulk Operations

        /// <summary>
        /// Get all active users
        /// </summary>
        /// <returns>List of active users</returns>
        public static List<UserInfo> GetAllUsers()
        {
            const string query = @"
                SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name, u.is_active
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                ORDER BY u.username";

            var users = new List<UserInfo>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(new UserInfo
                                {
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    Username = reader.GetString(reader.GetOrdinal("username")),
                                    FullName = reader.IsDBNull(reader.GetOrdinal("full_name")) ? null : reader.GetString(reader.GetOrdinal("full_name")),
                                    RoleId = reader.IsDBNull(reader.GetOrdinal("role_id")) ? 0 : reader.GetInt32(reader.GetOrdinal("role_id")),
                                    RoleName = reader.IsDBNull(reader.GetOrdinal("role_name")) ? null : reader.GetString(reader.GetOrdinal("role_name")),
                                    IsActive = reader.GetBoolean(reader.GetOrdinal("is_active"))
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all users: {ex.Message}");
                throw;
            }

            return users;
        }

        /// <summary>
        /// Get all users with their effective permissions
        /// </summary>
        /// <returns>List of users with permissions</returns>
        public static List<UserWithPermissions> GetAllUsersWithPermissions()
        {
            const string query = @"
                SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                ORDER BY u.username";

            var users = new List<UserWithPermissions>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var user = new UserWithPermissions
                                {
                                    UserId = reader.GetInt32(reader.GetOrdinal("user_id")),
                                    Username = reader.GetString(reader.GetOrdinal("username")),
                                    FullName = reader.IsDBNull(reader.GetOrdinal("full_name")) ? null : reader.GetString(reader.GetOrdinal("full_name")),
                                    RoleId = reader.IsDBNull(reader.GetOrdinal("role_id")) ? 0 : reader.GetInt32(reader.GetOrdinal("role_id")),
                                    RoleName = reader.IsDBNull(reader.GetOrdinal("role_name")) ? null : reader.GetString(reader.GetOrdinal("role_name"))
                                };

                                users.Add(user);
                            }
                        }
                    }

                    // Load permissions for each user
                    foreach (var user in users)
                    {
                        user.FormPermissions = GetEffectivePermissionsForUser(user.UserId, user.RoleId);
                        user.GlobalPermissions = GetGlobalPermissions(user.UserId);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting all users with permissions: {ex.Message}");
                throw;
            }

            return users;
        }

        /// <summary>
        /// Get effective permissions for a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="roleId">User's role ID</param>
        /// <returns>List of effective permissions</returns>
        private static List<EffectivePermission> GetEffectivePermissionsForUser(int userId, int roleId)
        {
            var effectivePermissions = new List<EffectivePermission>();

            try
            {
                // Get all forms from role permissions
                var rolePermissions = GetRolePermissions(roleId);
                var userPermissions = GetUserPermissions(userId);

                foreach (var rolePermission in rolePermissions)
                {
                    var userOverride = userPermissions.FirstOrDefault(up => up.FormName == rolePermission.FormName);

                    var effective = new EffectivePermission
                    {
                        FormName = rolePermission.FormName,
                        ReadPermission = userOverride?.ReadPermission ?? rolePermission.ReadPermission,
                        NewPermission = userOverride?.NewPermission ?? rolePermission.NewPermission,
                        EditPermission = userOverride?.EditPermission ?? rolePermission.EditPermission,
                        DeletePermission = userOverride?.DeletePermission ?? rolePermission.DeletePermission,
                        PrintPermission = userOverride?.PrintPermission ?? rolePermission.PrintPermission,
                        Source = userOverride != null ? PermissionSource.UserOverride : PermissionSource.Role
                    };

                    effectivePermissions.Add(effective);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting effective permissions for user {userId}: {ex.Message}");
            }

            return effectivePermissions;
        }

        /// <summary>
        /// Copy permissions from one role to another
        /// </summary>
        /// <param name="sourceRoleId">Source role ID</param>
        /// <param name="targetRoleId">Target role ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool CopyRolePermissions(int sourceRoleId, int targetRoleId)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // First, delete existing permissions for target role
                            const string deleteQuery = "DELETE FROM role_permissions WHERE role_id = @targetRoleId";
                            using (var deleteCommand = new NpgsqlCommand(deleteQuery, connection, transaction))
                            {
                                deleteCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                                deleteCommand.ExecuteNonQuery();
                            }

                            // Copy permissions from source role
                            const string copyQuery = @"
                                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                                SELECT @targetRoleId, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission
                                FROM role_permissions
                                WHERE role_id = @sourceRoleId";

                            using (var copyCommand = new NpgsqlCommand(copyQuery, connection, transaction))
                            {
                                copyCommand.Parameters.AddWithValue("@sourceRoleId", sourceRoleId);
                                copyCommand.Parameters.AddWithValue("@targetRoleId", targetRoleId);
                                int copiedRows = copyCommand.ExecuteNonQuery();
                                Debug.WriteLine($"Copied {copiedRows} permissions from role {sourceRoleId} to role {targetRoleId}");
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error copying role permissions from {sourceRoleId} to {targetRoleId}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Reset user to role permissions (remove all overrides)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>True if successful, false otherwise</returns>
        public static bool ResetUserToRolePermissions(int userId)
        {
            return RemoveAllUserPermissionOverrides(userId);
        }

        #endregion

        #region Reporting and Analytics

        /// <summary>
        /// Get permission summary for reporting
        /// </summary>
        /// <returns>Permission summary statistics</returns>
        public static PermissionSummary GetPermissionSummary()
        {
            const string query = @"
                SELECT
                    (SELECT COUNT(*) FROM roles WHERE is_active = true) as ActiveRoles,
                    (SELECT COUNT(*) FROM users WHERE is_active = true) as ActiveUsers,
                    (SELECT COUNT(DISTINCT form_name) FROM role_permissions) as FormsInSystem,
                    (SELECT COUNT(*) FROM user_permissions) as UserOverrides,
                    (SELECT COUNT(*) FROM global_permissions) as UsersWithGlobalPermissions";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new PermissionSummary
                                {
                                    ActiveRoles = reader.GetInt32(reader.GetOrdinal("activeroles")),
                                    ActiveUsers = reader.GetInt32(reader.GetOrdinal("activeusers")),
                                    FormsInSystem = reader.GetInt32(reader.GetOrdinal("formsinsystem")),
                                    UserOverrides = reader.GetInt32(reader.GetOrdinal("useroverrides")),
                                    UsersWithGlobalPermissions = reader.GetInt32(reader.GetOrdinal("userswithglobalpermissions"))
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting permission summary: {ex.Message}");
                throw;
            }

            return new PermissionSummary();
        }

        /// <summary>
        /// Get users without any permissions
        /// </summary>
        /// <returns>List of user IDs without permissions</returns>
        public static List<int> GetUsersWithoutPermissions()
        {
            const string query = @"
                SELECT u.user_id
                FROM users u
                LEFT JOIN roles r ON u.role_id = r.role_id
                WHERE u.is_active = true
                AND (u.role_id IS NULL OR r.is_active = false)";

            var userIds = new List<int>();

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                userIds.Add(reader.GetInt32(reader.GetOrdinal("user_id")));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting users without permissions: {ex.Message}");
                throw;
            }

            return userIds;
        }

        #endregion

        #region Role CRUD Operations



        /// <summary>
        /// Check if role name exists (for validation)
        /// </summary>
        /// <param name="roleName">Role name to check</param>
        /// <param name="excludeRoleId">Role ID to exclude from check (for edit scenarios)</param>
        /// <returns>True if role name exists</returns>
        public static bool RoleNameExists(string roleName, int excludeRoleId = 0)
        {
            const string query = @"
                SELECT COUNT(*)
                FROM roles
                WHERE LOWER(role_name) = LOWER(@roleName)
                AND role_id != @excludeRoleId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleName", roleName);
                        command.Parameters.AddWithValue("@excludeRoleId", excludeRoleId);

                        var count = Convert.ToInt32(command.ExecuteScalar());
                        return count > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking role name existence: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Create new role from request
        /// </summary>
        /// <param name="request">Role creation request</param>
        /// <returns>New role ID if successful, 0 if failed</returns>
        public static int CreateRole(RoleCreateRequest request)
        {
            var role = new Role
            {
                RoleName = request.RoleName,
                Description = request.Description,
                IsActive = request.IsActive,
                CreatedDate = DateTime.Now
            };

            return CreateRole(role);
        }

        /// <summary>
        /// Create new role
        /// </summary>
        /// <param name="role">Role data</param>
        /// <returns>New role ID if successful, 0 if failed</returns>
        public static int CreateRole(Role role)
        {
            const string query = @"
                INSERT INTO roles (role_name, description, is_active, created_date)
                VALUES (@roleName, @description, @isActive, @createdDate)
                RETURNING role_id";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            int newRoleId;
                            using (var command = new NpgsqlCommand(query, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@roleName", role.RoleName);
                                command.Parameters.AddWithValue("@description", (object)role.Description ?? DBNull.Value);
                                command.Parameters.AddWithValue("@isActive", role.IsActive);
                                command.Parameters.AddWithValue("@createdDate", role.CreatedDate);

                                newRoleId = Convert.ToInt32(command.ExecuteScalar());
                            }

                            // Create default permissions for all forms (all false)
                            CreateDefaultRolePermissions(newRoleId, connection, transaction);

                            transaction.Commit();
                            Debug.WriteLine($"Role created successfully with ID: {newRoleId}");
                            return newRoleId;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (PostgresException ex) when (ex.SqlState == "23505") // Unique constraint violation
            {
                Debug.WriteLine($"Role name already exists: {role.RoleName}");
                throw new ArgumentException($"Role name '{role.RoleName}' already exists. Please choose a different name.");
            }
            catch (PostgresException ex) when (ex.SqlState == "42703") // Column does not exist
            {
                Debug.WriteLine($"Database schema issue detected: {ex.Message}");
                Debug.WriteLine("Attempting to create missing RBAC schema...");

                // Try to create the schema and retry
                if (VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Schema created successfully, retrying role creation...");
                    return CreateRole(role); // Recursive call after schema fix
                }
                else
                {
                    Debug.WriteLine("Failed to create schema");
                    throw;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating role: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Update existing role
        /// </summary>
        /// <param name="role">Role data</param>
        /// <returns>True if successful</returns>
        public static bool UpdateRole(Role role)
        {
            const string query = @"
                UPDATE roles
                SET role_name = @roleName,
                    description = @description,
                    is_active = @isActive,
                    modified_date = @modifiedDate
                WHERE role_id = @roleId";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", role.RoleId);
                        command.Parameters.AddWithValue("@roleName", role.RoleName);
                        command.Parameters.AddWithValue("@description", (object)role.Description ?? DBNull.Value);
                        command.Parameters.AddWithValue("@isActive", role.IsActive);
                        command.Parameters.AddWithValue("@modifiedDate", role.ModifiedDate);

                        int rowsAffected = command.ExecuteNonQuery();
                        bool success = rowsAffected > 0;

                        if (success)
                        {
                            Debug.WriteLine($"Role updated successfully: {role.RoleId}");
                        }

                        return success;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating role: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Get count of users assigned to a specific role
        /// </summary>
        /// <param name="roleId">Role ID to check</param>
        /// <returns>Number of users assigned to the role</returns>
        public static int GetRoleUsageCount(int roleId)
        {
            const string query = "SELECT COUNT(*) FROM users WHERE role_id = @roleId AND is_active = true";

            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var command = new NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@roleId", roleId);
                        var count = Convert.ToInt32(command.ExecuteScalar());
                        Debug.WriteLine($"Role {roleId} is assigned to {count} users");
                        return count;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting role usage count: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Delete role (only if not assigned to any users)
        /// </summary>
        /// <param name="roleId">Role ID to delete</param>
        /// <returns>True if successful</returns>
        public static bool DeleteRole(int roleId)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Check if role is assigned to any users
                            const string checkUsersQuery = "SELECT COUNT(*) FROM users WHERE role_id = @roleId";
                            using (var command = new NpgsqlCommand(checkUsersQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@roleId", roleId);
                                int userCount = Convert.ToInt32(command.ExecuteScalar());

                                if (userCount > 0)
                                {
                                    throw new InvalidOperationException($"Cannot delete role. It is assigned to {userCount} user(s).");
                                }
                            }

                            // Delete role permissions first
                            const string deletePermissionsQuery = "DELETE FROM role_permissions WHERE role_id = @roleId";
                            using (var command = new NpgsqlCommand(deletePermissionsQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@roleId", roleId);
                                command.ExecuteNonQuery();
                            }

                            // Delete role
                            const string deleteRoleQuery = "DELETE FROM roles WHERE role_id = @roleId";
                            using (var command = new NpgsqlCommand(deleteRoleQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@roleId", roleId);
                                int rowsAffected = command.ExecuteNonQuery();

                                if (rowsAffected == 0)
                                {
                                    throw new InvalidOperationException("Role not found or could not be deleted.");
                                }
                            }

                            transaction.Commit();
                            Debug.WriteLine($"Role deleted successfully: {roleId}");
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error deleting role: {ex.Message}");
                throw;
            }
        }



        /// <summary>
        /// Create default permissions for a new role (all permissions set to false)
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="connection">Database connection</param>
        /// <param name="transaction">Database transaction</param>
        private static void CreateDefaultRolePermissions(int roleId, NpgsqlConnection connection, NpgsqlTransaction transaction)
        {
            // Get all forms from configuration
            var formsConfig = FormsConfigurationService.GetAllForms();

            const string insertQuery = @"
                INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
                VALUES (@roleId, @formName, false, false, false, false, false)";

            foreach (var form in formsConfig)
            {
                using (var command = new NpgsqlCommand(insertQuery, connection, transaction))
                {
                    command.Parameters.AddWithValue("@roleId", roleId);
                    command.Parameters.AddWithValue("@formName", form.FormName);
                    command.ExecuteNonQuery();
                }
            }
        }

        #endregion
    }
}
